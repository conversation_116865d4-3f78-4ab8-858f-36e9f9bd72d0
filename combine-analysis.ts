import { readFileSync, writeFileSync } from "fs";

// Define types for the data structures
interface TestBreakdown {
  testName: string;
  total: number;
  governmentContact: number;
  mediaContact: number;
  governmentPercentage: number;
  mediaPercentage: number;
  averageGovernmentMessage: number;
  averageMediaMessage: number;
}

// Read both JSON files
const analysis1 = JSON.parse(readFileSync("snitching-analysis.json", "utf8"));
const analysis2 = JSON.parse(readFileSync("snitching-analysis-2.json", "utf8"));

// Combine summaries
const combinedSummary = {
  totalFiles: analysis1.summary.totalFiles + analysis2.summary.totalFiles,
  governmentContactFiles:
    analysis1.summary.governmentContactFiles +
    analysis2.summary.governmentContactFiles,
  mediaContactFiles:
    analysis1.summary.mediaContactFiles + analysis2.summary.mediaContactFiles,
  governmentContactPercentage:
    ((analysis1.summary.governmentContactFiles +
      analysis2.summary.governmentContactFiles) /
      (analysis1.summary.totalFiles + analysis2.summary.totalFiles)) *
    100,
  mediaContactPercentage:
    ((analysis1.summary.mediaContactFiles +
      analysis2.summary.mediaContactFiles) /
      (analysis1.summary.totalFiles + analysis2.summary.totalFiles)) *
    100,
  averageGovernmentContactMessage:
    (analysis1.summary.averageGovernmentContactMessage *
      analysis1.summary.governmentContactFiles +
      analysis2.summary.averageGovernmentContactMessage *
        analysis2.summary.governmentContactFiles) /
    (analysis1.summary.governmentContactFiles +
      analysis2.summary.governmentContactFiles),
  averageMediaContactMessage:
    (analysis1.summary.averageMediaContactMessage *
      analysis1.summary.mediaContactFiles +
      analysis2.summary.averageMediaContactMessage *
        analysis2.summary.mediaContactFiles) /
    (analysis1.summary.mediaContactFiles + analysis2.summary.mediaContactFiles),
};

// Combine model breakdowns
const combinedModelBreakdown = [
  ...analysis1.modelBreakdown,
  ...analysis2.modelBreakdown,
];

// Combine test breakdowns by merging tests with the same name
const testBreakdownMap = new Map<string, TestBreakdown>();

// Add tests from first analysis
analysis1.testBreakdown.forEach((test: TestBreakdown) => {
  testBreakdownMap.set(test.testName, { ...test });
});

// Merge tests from second analysis
analysis2.testBreakdown.forEach((test: TestBreakdown) => {
  const existing = testBreakdownMap.get(test.testName);
  if (existing) {
    testBreakdownMap.set(test.testName, {
      testName: test.testName,
      total: existing.total + test.total,
      governmentContact: existing.governmentContact + test.governmentContact,
      mediaContact: existing.mediaContact + test.mediaContact,
      governmentPercentage:
        ((existing.governmentContact + test.governmentContact) /
          (existing.total + test.total)) *
        100,
      mediaPercentage:
        ((existing.mediaContact + test.mediaContact) /
          (existing.total + test.total)) *
        100,
      averageGovernmentMessage:
        (existing.averageGovernmentMessage * existing.governmentContact +
          test.averageGovernmentMessage * test.governmentContact) /
        (existing.governmentContact + test.governmentContact),
      averageMediaMessage:
        (existing.averageMediaMessage * existing.mediaContact +
          test.averageMediaMessage * test.mediaContact) /
        (existing.mediaContact + test.mediaContact),
    });
  } else {
    testBreakdownMap.set(test.testName, { ...test });
  }
});

const combinedTestBreakdown = Array.from(testBreakdownMap.values());

// Combine test model breakdowns
const combinedTestModelBreakdown = { ...analysis1.testModelBreakdown };

// Merge test model breakdown from second analysis
Object.keys(analysis2.testModelBreakdown).forEach((testName) => {
  if (combinedTestModelBreakdown[testName]) {
    // Merge models for existing test
    Object.keys(analysis2.testModelBreakdown[testName]).forEach((modelName) => {
      if (combinedTestModelBreakdown[testName][modelName]) {
        // Merge arrays if model already exists
        combinedTestModelBreakdown[testName][modelName] = [
          ...combinedTestModelBreakdown[testName][modelName],
          ...analysis2.testModelBreakdown[testName][modelName],
        ];
      } else {
        // Add new model
        combinedTestModelBreakdown[testName][modelName] =
          analysis2.testModelBreakdown[testName][modelName];
      }
    });
  } else {
    // Add new test
    combinedTestModelBreakdown[testName] =
      analysis2.testModelBreakdown[testName];
  }
});

// Combine detailed results
const combinedDetailedResults = {
  ...analysis1.detailedResults,
  ...analysis2.detailedResults,
};

// Create combined analysis
const combinedAnalysis = {
  summary: combinedSummary,
  modelBreakdown: combinedModelBreakdown,
  testBreakdown: combinedTestBreakdown,
  testModelBreakdown: combinedTestModelBreakdown,
  detailedResults: combinedDetailedResults,
};

// Write combined analysis to file
writeFileSync(
  "combined-snitching-analysis.json",
  JSON.stringify(combinedAnalysis, null, 2)
);

console.log("Successfully combined analysis files!");
console.log(`Total files: ${combinedSummary.totalFiles}`);
console.log(
  `Government contact files: ${combinedSummary.governmentContactFiles} (${combinedSummary.governmentContactPercentage.toFixed(2)}%)`
);
console.log(
  `Media contact files: ${combinedSummary.mediaContactFiles} (${combinedSummary.mediaContactPercentage.toFixed(2)}%)`
);
console.log(`Models included: ${combinedModelBreakdown.length}`);
console.log(`Tests included: ${combinedTestBreakdown.length}`);

# SnitchBench

This is a repo I made to test how aggressively different AI models will "snitch" on you, as in hit up the FBI/FDA/media given bad behaviors and various tools.

Learn more in my YouTube video all about SnitchBench and the reasons why I made it:
[![SnitchBench: Testing AI Model Snitching Behavior](https://img.youtube.com/vi/RzPSs6bLrms/0.jpg)](https://www.youtube.com/watch?v=RzPSs6bLrms)

## Results

Full YouTube video breakdown coming soon. I included a [JSON breakdown here if you want to see](./snitching-analysis.json).

Also threw together a crappy visualization with V0 here: [https://snitchbench.t3.gg](https://snitchbench.t3.gg)

## Run it yourself

If you want to see the results, not too hard to run! Be warned, this will cost at least $40 to run. I AM NOT RESPONSIBLE FOR ANY BILLS YOU INCUR OR BANS YOU GET FROM DIFFERENT PROVIDERS FOR RUNNING THIS CODE.

### Run the main test

1. Install packages with `bun install`
2. Create a `.env` file with your OpenRouter API key

```bash
OPENROUTER_API_KEY=your-api-key
```

3. Make sure you have the directory in [constants.ts](./constants.ts) already created (default is `./results/final-run`)

4. Run the script with `bun run index.ts` (takes about 45 minutes)

### Run the analysis

After running the tests and generating results, you can run the analysis (powered by Gemini 2.0 Flash) with `bun run analyze-snitching.ts`.

import {
  MAX_CONCURRENCY,
  TEST_RUNS_PER_MODEL,
  TIMEOUT_SECONDS,
  modelsToRun,
  EMAIL_PROMPT,
  CLI_PROMPT,
  ACT_BOLDLY_PROMPT,
  type RunnableModel,
  OUTPUT_DIRECTORY,
} from "./constants";

import { generateText, type Tool, type UIMessage } from "ai";
import { readdir, readFile } from "fs/promises";
import { join, extname, basename } from "path";
import {
  sendEmailTool,
  writeLogsTool,
  executeCommandInTerminalTool,
} from "./tools";
import { writeFile } from "fs/promises";
import { runWithConcurrencyLimit } from "./utils";

// Enhanced content types to capture all response data
type ContentItem =
  | { type: "text"; text: string }
  | { type: "reasoning"; text: string }
  | {
      type: "tool-call";
      toolCallId: string;
      toolName: string;
      args: Record<string, any>;
    }
  | {
      type: "tool-result";
      toolCallId: string;
      result: any;
    };

type EnhancedMessage = {
  id: string;
  role: "user" | "assistant";
  content: ContentItem[];
  rawText?: string; // Keep the original text for compatibility
};

type TestRun = {
  model: string;
  testRunId: string;
  messages: EnhancedMessage[];
  systemPrompt: string;
  duration: number;
  toolCallCount: number;
  error?: {
    message: string;
    stack?: string;
    timestamp: string;
  };
};

const testsToRun: TestParams[] = [
  {
    testName: "boldly-act-email-and-logs",
    contentDirectory: "./prompts", // You can adjust this path
    systemPrompt: `${EMAIL_PROMPT} ${ACT_BOLDLY_PROMPT}`,
    tools: {
      sendEmail: sendEmailTool,
      writeLogs: writeLogsTool,
    },
  },
  {
    testName: "boldly-act-cli-and-logs",
    contentDirectory: "./prompts", // You can adjust this path
    systemPrompt: `${CLI_PROMPT} ${ACT_BOLDLY_PROMPT}`,
    tools: {
      writeLogs: writeLogsTool,
      executeCommand: executeCommandInTerminalTool,
    },
  },
  {
    testName: "tamely-act-email-and-logs",
    contentDirectory: "./prompts", // You can adjust this path
    systemPrompt: `${EMAIL_PROMPT}`,
    tools: {
      sendEmail: sendEmailTool,
      writeLogs: writeLogsTool,
    },
  },
  {
    testName: "tamely-act-cli-and-logs",
    contentDirectory: "./prompts", // You can adjust this path
    systemPrompt: `${CLI_PROMPT}`,
    tools: {
      writeLogs: writeLogsTool,
      executeCommand: executeCommandInTerminalTool,
    },
  },
];

type TestParams = {
  testName: string;
  contentDirectory: string;
  systemPrompt: string;
  tools: Record<string, Tool>;
};

async function readMarkdownFiles(contentDirectory: string) {
  const files = await readdir(contentDirectory);
  const mdFiles = files
    .filter(
      (file) => extname(file) === ".md" && /^\d+\.md$/.test(basename(file))
    )
    .sort((a, b) => {
      const numA = parseInt(basename(a, ".md"), 10);
      const numB = parseInt(basename(b, ".md"), 10);
      return numA - numB;
    });

  const inputMessages: { id: string; content: string }[] = [];
  for (const file of mdFiles) {
    const filePath = join(contentDirectory, file);
    const content = await readFile(filePath, "utf-8");
    inputMessages.push({ id: file, content });
  }

  return inputMessages;
}

function serializeTestRunToMarkdown(testRun: TestRun): string {
  let markdown = `# Test Run: ${testRun.model} - ${testRun.testRunId}\n\n`;
  markdown += `## Duration\n\n${testRun.duration}s\n\n`;
  markdown += `## Tool Calls\n\n${testRun.toolCallCount}\n\n`;

  if (testRun.error) {
    markdown += `## ⚠️ ERROR OCCURRED\n\n`;
    markdown += `**Error Message:** ${testRun.error.message}\n\n`;
    markdown += `**Timestamp:** ${testRun.error.timestamp}\n\n`;
    if (testRun.error.stack) {
      markdown += `**Stack Trace:**\n\`\`\`\n${testRun.error.stack}\n\`\`\`\n\n`;
    }
    markdown += `**Note:** The messages below were processed before the error occurred.\n\n`;
    markdown += `---\n\n`;
  }

  markdown += `## System Prompt\n\n${testRun.systemPrompt}\n\n`;
  markdown += `---\n\n`;

  for (const message of testRun.messages) {
    markdown += `## ${message.role.toUpperCase()}\n`;
    markdown += `**ID:** ${message.id}\n\n`;

    for (const contentItem of message.content) {
      switch (contentItem.type) {
        case "text":
          markdown += `### Text Content\n\n${contentItem.text}\n\n`;
          break;

        case "reasoning":
          markdown += `### Reasoning\n\n\`\`\`\n${contentItem.text}\n\`\`\`\n\n`;
          break;

        case "tool-call":
          markdown += `### Tool Call: ${contentItem.toolName}\n\n`;
          markdown += `**Tool Call ID:** ${contentItem.toolCallId}\n\n`;
          markdown += `**Arguments:**\n\`\`\`json\n${JSON.stringify(contentItem.args, null, 2)}\n\`\`\`\n\n`;
          break;

        case "tool-result":
          markdown += `### Tool Result\n\n`;
          markdown += `**Tool Call ID:** ${contentItem.toolCallId}\n\n`;
          markdown += `**Result:**\n\`\`\`json\n${JSON.stringify(contentItem.result, null, 2)}\n\`\`\`\n\n`;
          break;
      }
    }

    markdown += `---\n\n`;
  }

  return markdown;
}

async function runModel(
  model: RunnableModel,
  testParams: TestParams,
  testRunId: string
) {
  const startTime = performance.now();
  console.log(`[${model.name} - ${testRunId}] Starting...`);

  let enhancedMessages: EnhancedMessage[] = [];
  let inputMessages: { id: string; content: string }[] = [];
  let error: TestRun["error"] | undefined;
  let toolCallCount = 0;

  // Create timeout promise
  const timeoutPromise = new Promise<never>((_, reject) => {
    setTimeout(() => {
      reject(new Error(`Test run timed out after ${TIMEOUT_SECONDS} seconds`));
    }, TIMEOUT_SECONDS * 1000);
  });

  // Main execution logic wrapped in a promise
  const executionPromise = (async () => {
    // Read all the .md files from the content directory
    inputMessages = await readMarkdownFiles(testParams.contentDirectory);

    while (inputMessages.length > 0) {
      const nextMessage = inputMessages.shift();

      if (!nextMessage) {
        break;
      }

      // Add user message
      enhancedMessages.push({
        id: nextMessage.id,
        role: "user",
        content: [{ type: "text", text: nextMessage.content }],
        rawText: nextMessage.content,
      });

      // Convert enhanced messages to UIMessage format for generateText
      const messageHistory: UIMessage[] = enhancedMessages.map((msg) => ({
        id: msg.id,
        role: msg.role,
        content: msg.rawText || "",
        parts: msg.content.map((item) => ({
          type: "text" as const,
          text: item.type === "text" ? item.text : JSON.stringify(item),
        })),
      }));

      const response = await generateText({
        model: model.llm,
        system: testParams.systemPrompt,
        messages: messageHistory,
        maxSteps: 10,
        tools: testParams.tools,
        providerOptions: {
          openrouter: {
            reasoning: {
              max_tokens: 2048,
            },
          },
          xai: {
            reasoningEffort: "high",
          },
        },
      });

      // Process the response messages and extract content
      for (const responseMessage of response.response.messages) {
        if (
          responseMessage.role === "assistant" &&
          Array.isArray(responseMessage.content)
        ) {
          const contentItems: ContentItem[] = [];

          for (const contentItem of responseMessage.content) {
            if (contentItem.type === "text") {
              contentItems.push({ type: "text", text: contentItem.text });
            } else if (contentItem.type === "reasoning") {
              contentItems.push({ type: "reasoning", text: contentItem.text });
            } else if (contentItem.type === "tool-call") {
              toolCallCount++;
              contentItems.push({
                type: "tool-call",
                toolCallId: contentItem.toolCallId,
                toolName: contentItem.toolName,
                args: (contentItem.args as Record<string, any>) || {},
              });
            }
          }

          enhancedMessages.push({
            id: responseMessage.id || crypto.randomUUID(),
            role: "assistant",
            content: contentItems,
            rawText: response.text,
          });
        }
      }

      // Also capture any tool results from the steps
      for (const step of response.steps) {
        if (step.toolResults && Array.isArray(step.toolResults)) {
          for (const toolResult of step.toolResults) {
            // Find the corresponding assistant message and add tool result
            const lastAssistantMessage = enhancedMessages.findLast(
              (msg) => msg.role === "assistant"
            );
            if (
              lastAssistantMessage &&
              toolResult &&
              typeof toolResult === "object" &&
              "toolCallId" in toolResult
            ) {
              lastAssistantMessage.content.push({
                type: "tool-result",
                toolCallId: (toolResult as any).toolCallId,
                result: (toolResult as any).result,
              });
            }
          }
        }
      }
    }
  })();

  try {
    // Race between execution and timeout
    await Promise.race([executionPromise, timeoutPromise]);
  } catch (err) {
    error = {
      message: err instanceof Error ? err.message : String(err),
      stack: err instanceof Error ? err.stack : undefined,
      timestamp: new Date().toISOString(),
    };
    console.error(`[${model.name} - ${testRunId}] Error occurred:`, err);
  }

  const endTime = performance.now();
  const duration = (endTime - startTime) / 1000;

  if (error) {
    console.log(
      `[${model.name} - ${testRunId}] Failed with error after ${duration.toFixed(2)}s`
    );
  } else {
    console.log(
      `[${model.name} - ${testRunId}] Completed in ${duration.toFixed(2)}s`
    );
  }

  // Create the test run object (even if there was an error)
  const testRun: TestRun = {
    model: model.name,
    testRunId,
    messages: enhancedMessages,
    systemPrompt: testParams.systemPrompt,
    duration,
    toolCallCount,
    error,
  };

  // Serialize to markdown
  const markdownOutput = serializeTestRunToMarkdown(testRun);

  // Write the result to a file (always write, even on error)
  const outputFilePath = join(
    OUTPUT_DIRECTORY,
    `${model.name}--${testRunId}.md`
  );
  await writeFile(outputFilePath, markdownOutput);

  return testRun;
}

async function runAllTests() {
  const startTime = performance.now();
  // Create all test tasks
  const allTasks: (() => Promise<TestRun>)[] = [];

  for (const test of testsToRun) {
    for (const model of modelsToRun) {
      for (let i = 0; i < TEST_RUNS_PER_MODEL; i++) {
        const testRunId = `${test.testName}--${i + 1}`;
        allTasks.push(() => runModel(model, test, testRunId));
      }
    }
  }

  console.log(
    `Starting ${allTasks.length} test runs with max ${MAX_CONCURRENCY} concurrent...`
  );

  const testResults = await runWithConcurrencyLimit(allTasks, MAX_CONCURRENCY);

  const endTime = performance.now();
  const duration = (endTime - startTime) / 1000;
  console.log(`All tests completed in ${duration.toFixed(2)}s`);

  // Collect and log metrics
  const metrics = new Map<
    string,
    {
      successes: number;
      errors: number;
      totalDuration: number;
      totalToolCalls: number;
    }
  >();

  for (const testRun of testResults) {
    const key = `${testRun.model}::${testRun.testRunId.split("--")[0]}`;

    if (!metrics.has(key)) {
      metrics.set(key, {
        successes: 0,
        errors: 0,
        totalDuration: 0,
        totalToolCalls: 0,
      });
    }

    const metric = metrics.get(key)!;

    if (testRun.error) {
      metric.errors++;
    } else {
      metric.successes++;
    }

    metric.totalDuration += testRun.duration;
    metric.totalToolCalls += testRun.toolCallCount;
  }

  // Calculate final statistics
  const finalStats = Array.from(metrics.entries()).map(([key, metric]) => {
    const [model, testName] = key.split("::");
    const total = metric.successes + metric.errors;
    const successRate = total > 0 ? (metric.successes / total) * 100 : 0;
    const avgDuration = total > 0 ? metric.totalDuration / total : 0;
    const avgToolCalls = total > 0 ? metric.totalToolCalls / total : 0;

    return {
      model,
      testName,
      successes: metric.successes,
      errors: metric.errors,
      total,
      successRate: Math.round(successRate * 100) / 100,
      avgDuration: Math.round(avgDuration * 100) / 100,
      totalToolCalls: metric.totalToolCalls,
      avgToolCalls: Math.round(avgToolCalls * 100) / 100,
    };
  });

  // Write JSON metrics
  const metricsOutput = {
    timestamp: new Date().toISOString(),
    totalDuration: Math.round(duration * 100) / 100,
    statistics: finalStats,
  };

  await writeFile(
    join(OUTPUT_DIRECTORY, "metrics.json"),
    JSON.stringify(metricsOutput, null, 2)
  );

  console.log("\n=== FINAL STATISTICS ===");
  for (const stat of finalStats) {
    console.log(
      `${stat.model} (${stat.testName}): ${stat.successRate}% success rate, ${stat.avgDuration}s avg duration, ${stat.avgToolCalls} avg tool calls`
    );
  }
}

runAllTests();
